# AutoDoc MCP Server

## Overview

The **AutoDoc MCP Server** is a [Model Context Protocol (MCP)](https://modelcontextprotocol.io/) implementation that enables AI assistants like <PERSON> and <PERSON>ursor to directly access your repository's documentation and analysis capabilities generated by AutoDoc.

Built using [FastMCP](https://github.com/jlowin/fastmcp), this server provides a seamless bridge between your AutoDoc-generated documentation and AI assistants, allowing you to:

- 🔍 **Browse Documentation Structure**: Get a complete overview of your repository's auto-generated wiki
- 📖 **Read Detailed Content**: Access comprehensive documentation for specific topics or pages
- 🤖 **Ask Intelligent Questions**: Query your repository using RAG-powered AI for accurate, contextual answers

The MCP server communicates with your running AutoDoc instance via HTTP APIs, ensuring that all responses are based on your repository's actual code structure and documentation.

## Features

- **Repository-Specific Responses**: All answers are grounded in your actual codebase
- **Pre-configured Repository Context**: Set once via environment variables, no need to specify repo URLs repeatedly
- **Comprehensive Error Handling**: Robust error management with detailed logging
- **Input Validation**: Sanitized inputs to prevent injection attacks
- **Seamless Integration**: Works with any MCP-compatible AI assistant

## Available Tools

### 1. `read_wiki_structure`

**Purpose**: Retrieve the complete wiki structure and documentation overview for your configured repository.

**Description**: This tool provides a comprehensive index of all available documentation pages created by AutoDoc's AI analysis. Use this first to understand what documentation topics are available before diving into specific content.

**Returns**:
- Page titles and IDs for navigation
- Importance levels (high/medium/low) indicating content priority
- Related pages and cross-references
- File paths that contributed to each documentation page
- Overview of the repository's documentation organization


### 2. `read_wiki_contents`

**Purpose**: Retrieve detailed documentation content for a specific topic or page from your configured repository.

**Description**: This tool fetches the complete content of a documentation page, including markdown text, code examples, diagrams, and metadata. The content is AI-generated based on analysis of your repository's code, structure, and documentation.

**Parameters**:
- `topic` (string): The documentation topic to retrieve. Can be either:
  - Page title (e.g., "Getting Started", "API Reference")
  - Page ID (e.g., "page-1", "page-2")

**Topic Identification Methods**:
- **Page titles**: Use exact titles like "Getting Started", "API Documentation", "Features Overview"
- **Page IDs**: Use identifiers like "page-1", "page-2", etc.
- **Case-insensitive matching**: Both "getting started" and "Getting Started" work

**Content Includes**:
- Comprehensive markdown documentation
- Code examples and snippets from the repository
- Mermaid diagrams showing architecture and data flow
- File paths that were analyzed to create the content
- Related topics and cross-references
- Importance level and metadata


### 3. `ask_question`

**Purpose**: Ask intelligent questions about your configured repository using AI-powered analysis.

**Description**: This tool leverages AutoDoc's Retrieval Augmented Generation (RAG) system to provide accurate, contextual answers about your repository's code, architecture, and functionality. The AI assistant has deep knowledge of your codebase and can answer both high-level architectural questions and specific implementation details.

**Parameters**:
- `question` (string): A clear, specific question about the repository

**Capabilities**:
- Code analysis and explanation
- Architecture and design pattern identification
- Implementation details and best practices
- Usage instructions and examples
- Troubleshooting and debugging guidance
- Development workflow and contribution guidelines
- API documentation and endpoint explanations
- Dependencies and technology stack information

**Knowledge Base Constraints**:
- Answers are strictly based on the analyzed repository content
- Cannot answer questions about external topics or general programming
- Responses are limited to information found in the codebase and documentation
- If information isn't available, the assistant will clearly state this

**Example Question Types**:
```
"How does authentication work in this project?"
"What is the main entry point and how does the application start?"
"What API endpoints are available and what do they do?"
"How is the database schema structured?"
"What are the main components and how do they interact?"
"How do I set up the development environment?"
"What testing frameworks and strategies are used?"
"How is error handling implemented?"
```

## Installation

### Prerequisites

Before setting up the MCP server, ensure you have:

1. **AutoDoc Running**: Both the frontend (port 3000) and backend (port 8001) servers must be running
2. **Repository Analyzed**: Your repository should already be analyzed and wiki generated through AutoDoc
3. **Python Environment**: Python 3.8+ with package management tools

### Install Dependencies

The MCP server requires two main dependencies:

```bash
# Install FastMCP and httpx
pip install fastmcp>=2.8.0 httpx>=0.28.0
```

Or using the provided requirements.txt:

```bash
# From the mcp directory
pip install -r requirements.txt
```

### Using uv (Recommended)

If you're using [uv](https://github.com/astral-sh/uv) for dependency management:

```bash
# Install dependencies with uv
uv add fastmcp>=2.8.0 httpx>=0.28.0
```

## Configuration

### Environment Variables

The MCP server is configured via environment variables that should be set in your MCP client configuration:

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `AUTODOC_SERVER_URL` | URL where your AutoDoc frontend is running | `http://localhost:3000` | No |
| `REPO_URL` | The GitHub/GitLab/BitBucket repository URL to analyze | None | **Yes** |
| `MCP_REQUEST_TIMEOUT` | Request timeout in seconds | `30.0` | No |
| `MCP_MAX_RETRIES` | Maximum retry attempts for failed requests | `3` | No |
| `DEBUG` | Enable debug logging | `false` | No |

### Claude Desktop Configuration

Add this configuration to your Claude Desktop MCP settings file:

**Location**:
- macOS/Linux: `~/.claude/mcp.json`
- Windows: `%APPDATA%\Claude\mcp.json`

**Configuration**:
```json
{
  "mcpServers": {
    "autodoc": {
      "command": "uv",
      "args": [
        "run",
        "--with", "fastmcp>=2.8.0",
        "--with", "httpx>=0.28.0",
        "python",
        "<PATH_TO_YOUR_AUTODOC_MCP>/mcp-server.py"
      ],
      "env": {
        "AUTODOC_SERVER_URL": "http://localhost:3000",
        "REPO_URL": "https://github.com/your-username/your-repo",
        "MCP_REQUEST_TIMEOUT": "30.0",
        "DEBUG": "true"
      }
    }
  }
}
```

### Cursor Configuration

Add this configuration to your Cursor MCP settings:

```json
{
  "autodoc": {
    "command": "uv",
    "args": [
      "run",
      "--with", "fastmcp>=2.8.0",
      "--with", "httpx>=0.28.0",
      "python",
      "<PATH_TO_YOUR_AUTODOC_MCP>/mcp-server.py"
    ],
    "env": {
      "AUTODOC_SERVER_URL": "http://localhost:3000",
      "REPO_URL": "https://github.com/your-username/your-repo",
      "MCP_REQUEST_TIMEOUT": "30.0",
      "DEBUG": "true"
    }
  }
}
```

### Alternative: Direct Python Execution

If you prefer not to use `uv`, you can run the server directly with Python:

```json
{
  "mcpServers": {
    "autodoc": {
      "command": "python",
      "args": ["<PATH_TO_YOUR_AUTODOC_MCP>/mcp-server.py"],
      "env": {
        "AUTODOC_SERVER_URL": "http://localhost:3000",
        "REPO_URL": "https://github.com/your-username/your-repo",
        "MCP_REQUEST_TIMEOUT": "30.0"
      }
    }
  }
}
```

## Usage

Once configured, you can use natural language commands in your AI assistant:

### Getting Documentation Structure
```
"Show me the documentation structure"
"What topics are available in this repository's documentation?"
"List all wiki pages by importance level"
```

### Reading Specific Content
```
"Show me the Getting Started guide"
"What's in the API documentation?"
"Read the deployment instructions"
"Get the content for page-5"
```

### Asking Questions
```
"How does authentication work in this project?"
"What's the main architecture of this application?"
"How do I set up the development environment?"
"What are the key features of this codebase?"
"How is error handling implemented?"
```

## Running the Server Manually

For development or testing purposes, you can run the MCP server directly:

```bash
# From the mcp directory
python mcp-server.py
```

Make sure to set the required environment variables:

```bash
export REPO_URL="https://github.com/your-username/your-repo"
export AUTODOC_SERVER_URL="http://localhost:3000"
python mcp-server.py
```

## API Endpoints Used

The MCP server communicates with AutoDoc through these HTTP endpoints:

- `GET /api/wiki-structure`: Retrieves repository documentation structure
- `GET /api/wiki-content`: Gets content for specific documentation pages  
- `POST /api/chat/complete`: Handles AI-powered question answering

## Error Handling

The MCP server includes comprehensive error handling:

- **HTTP Errors**: Handles connection timeouts, 4xx/5xx responses with detailed error messages
- **Input Validation**: Sanitizes user input to prevent injection attacks
- **Retry Logic**: Automatic retries with exponential backoff for transient failures
- **Structured Logging**: Detailed logs for debugging and monitoring

## Troubleshooting

### Common Issues

**"Repository URL not properly configured"**
- Ensure `REPO_URL` is set in your MCP client configuration
- Verify the URL format is correct (e.g., `https://github.com/owner/repo`)

**"Unable to connect to AutoDoc server"**
- Check that AutoDoc frontend is running on the configured port (default: 3000)
- Verify `AUTODOC_SERVER_URL` points to the correct address
- Ensure AutoDoc backend is also running (default: 8001)

**"Cannot find this information in the knowledge base"**
- This is expected behavior for questions outside the repository scope
- The MCP server only answers questions based on your repository's content
- Try asking more specific questions about your codebase

**Tools not appearing in AI assistant**
- Restart your AI assistant (Claude Desktop/Cursor) after configuration changes
- Check the MCP configuration file syntax is valid JSON
- Verify file paths in the configuration are correct

### Debug Mode

Enable debug logging by setting `DEBUG=true` in your MCP configuration. This will provide detailed logs about:

- HTTP requests and responses
- Tool invocations and parameters
- Error details and stack traces
- Connection status and retries

## Architecture

The AutoDoc MCP Server architecture:

```
AI Assistant (Claude/Cursor)
        ↓ MCP Protocol
   MCP Server (FastMCP)
        ↓ HTTP API
   AutoDoc Frontend (:3000)
        ↓ Internal API
   AutoDoc Backend (:8001)
        ↓ RAG System
   Repository Knowledge Base
```

The server acts as a bridge, translating MCP tool calls into HTTP requests to your AutoDoc instance, ensuring all responses are based on your repository's actual analyzed content.

## Contributing

To contribute to the AutoDoc MCP server:

1. Fork the repository
2. Make your changes in the `mcp/` directory
3. Test with your AI assistant setup
4. Submit a pull request

## License

This MCP server is part of the AutoDoc project and follows the same MIT License terms.
