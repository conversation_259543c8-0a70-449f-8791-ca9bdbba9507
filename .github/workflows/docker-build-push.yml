name: Build and Push Docker Image

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  # Allow manual trigger
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  
concurrency:
  # This concurrency group ensures that only one job in the group runs at a time.
  # If a new job is triggered, the previous one will be canceled.
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
  
jobs:
  build-and-push:
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            platform: linux/amd64
          - os: ubuntu-24.04-arm
            platform: linux/arm64
    runs-on: ${{ matrix.os }}
    permissions:
      contents: read
      packages: write

    steps:
      - name: Prepare environment for current platform
        id: prepare
        run: |
          platform=${{ matrix.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV
          echo "GHCR_IMAGE=ghcr.io/${GITHUB_REPOSITORY@L}" >> $GITHUB_ENV
      
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.GHCR_IMAGE }}

      - name: Create empty .env file for build
        run: touch .env

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        id: build
        with:
          context: .
          platforms: ${{ matrix.platform }}
          push: ${{ github.event_name != 'pull_request' }}
          annotations: ${{ steps.meta.outputs.annotations }}
          labels: ${{ steps.meta.outputs.labels }}
          outputs: type=image,name=${{ env.GHCR_IMAGE }},push-by-digest=true,name-canonical=true,push=${{ github.event_name != 'pull_request' }},oci-mediatypes=true
          cache-from: type=gha,scope=${{ github.repository }}-${{ github.ref_name }}-${{ matrix.platform }}
          cache-to: type=gha,mode=max,scope=${{ github.repository }}-${{ github.ref_name }}-${{ matrix.platform }}
          
      - name: Export digest
        run: |
          mkdir -p /tmp/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "/tmp/digests/${digest#sha256:}"

      - name: Upload digest 
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ env.PLATFORM_PAIR }}
          path: /tmp/digests/*
          if-no-files-found: error
          retention-days: 1
  
  merge:
    name: merge Docker manifests
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'
    permissions:
      contents: read
      packages: write

    needs:
      - build-and-push
    steps:
      - name: Prepare environment
        id: prepare
        run: |
          echo "GHCR_IMAGE=ghcr.io/${GITHUB_REPOSITORY@L}" >> $GITHUB_ENV
      
      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: /tmp/digests
          pattern: digests-*
          merge-multiple: true


      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.GHCR_IMAGE }}
          annotations: |
            type=org.opencontainers.image.description,value=${{ github.event.repository.description || 'No description provided' }}
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,format=short
            type=ref,event=branch
            type=ref,event=pr
            latest

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: |
            network=host

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Get execution timestamp with RFC3339 format
        id: timestamp
        run: |
          echo "timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")" >> $GITHUB_OUTPUT

      - name: Create manifest list and pushs
        working-directory: /tmp/digests
        id: manifest-annotate
        continue-on-error: true
        run: |
              docker buildx imagetools create \
                $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
                --annotation='index:org.opencontainers.image.description=${{ github.event.repository.description }}' \
                --annotation='index:org.opencontainers.image.created=${{ steps.timestamp.outputs.timestamp }}' \
                --annotation='index:org.opencontainers.image.url=${{ github.event.repository.url }}' \
                --annotation='index:org.opencontainers.image.source=${{ github.event.repository.url }}' \
                $(printf '${{ env.GHCR_IMAGE }}@sha256:%s ' *)

      - name: Create manifest list and push without annotations
        if: steps.manifest-annotate.outcome == 'failure'
        working-directory: /tmp/digests
        run: |
              docker buildx imagetools create  $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
                $(printf '${{ env.GHCR_IMAGE }}@sha256:%s ' *)

      - name: Inspect image
        id: inspect
        run: |
          docker buildx imagetools inspect '${{ env.GHCR_IMAGE }}:${{ steps.meta.outputs.version }}'
