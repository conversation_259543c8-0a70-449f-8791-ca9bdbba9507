version: '3.8'

services:
  autodoc:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "${PORT:-8001}:${PORT:-8001}"  # API port
      - "${NEXT_PORT:-3000}:${NEXT_PORT:-3000}"  # Next.js port (configurable)
    env_file:
      - .env
    environment:
      - PORT=${PORT:-8001}
      - NEXT_PORT=${NEXT_PORT:-3000}
      - NODE_ENV=production
      - SERVER_BASE_URL=http://localhost:${PORT:-8001}
      - NEXT_PUBLIC_SERVER_BASE_URL=http://localhost:${PORT:-8001}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FILE_PATH=${LOG_FILE_PATH:-api/logs/application.log}
    volumes:
      - ~/.adalflow:/root/.adalflow      # Persist repository and embedding data
      - ./api/logs:/app/api/logs          # Persist log files across container restarts
    # Resource limits for docker-compose up (not Swarm mode)
    mem_limit: 6g
    mem_reservation: 2g
    # Restart policy to handle crashes gracefully
    restart: unless-stopped
    # Health check configuration
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PORT:-8001}/health"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s
    # Ensure clean shutdown
    stop_grace_period: 30s
